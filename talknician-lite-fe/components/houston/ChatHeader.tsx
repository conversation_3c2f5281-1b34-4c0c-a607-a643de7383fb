"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>u,
  Settings,
  Zap,
  StopCircle,
  Lock,
  Globe,
  Share2,
  User,
  Co<PERSON>,
  Link,
} from "lucide-react";
import {
  Conversation,
  StreamingState,
  ChatPersona,
  ConversationVisibility,
} from "@/types/chat";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { PersonaPill } from "@/components/houston/PersonaPill";
import { useToast } from "@/components/ui/use-toast";

interface ChatHeaderProps {
  currentConversation: Conversation | null;
  streamingState: StreamingState;
  currentUserId?: string;
  onMobileMenuToggle: () => void;
  onSettingsClick: () => void;
  onStopStreaming: () => void;
  onUpdateVisibility?: (
    conversationId: string,
    visibility: ConversationVisibility
  ) => void;
  currentPersona?: ChatPersona | null;
}

export function ChatHeader({
  currentConversation,
  streamingState,
  currentUserId,
  onMobileMenuToggle,
  onSettingsClick,
  onStopStreaming,
  onUpdateVisibility,
  currentPersona,
}: ChatHeaderProps) {
  const { toast } = useToast();
  const [isCopying, setIsCopying] = useState(false);

  // Helper functions
  const isOwner = (conversation: Conversation) => {
    return conversation.userId === currentUserId;
  };

  const getVisibilityIcon = (visibility: ConversationVisibility) => {
    return visibility === "private" ? (
      <Lock className="w-3 h-3 text-slate-400" />
    ) : (
      <Globe className="w-3 h-3 text-green-500" />
    );
  };

  const getAuthorDisplayName = (conversation: Conversation) => {
    if (isOwner(conversation)) {
      return "You";
    }
    return (
      conversation.user?.name || conversation.user?.email || "Unknown User"
    );
  };

  const canShareConversation = (conversation: Conversation) => {
    // Can share if user owns the conversation or if it's public
    return isOwner(conversation) || conversation.visibility === "public";
  };

  const copyConversationLink = async (conversation: Conversation) => {
    if (!canShareConversation(conversation)) {
      toast({
        title: "Cannot share conversation",
        description: "This is a private conversation that you don't own.",
        variant: "destructive",
      });
      return;
    }

    setIsCopying(true);
    try {
      const url = `${window.location.origin}/houston/${conversation.id}`;

      // Try to use the modern clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(url);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = url;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        textArea.remove();
      }

      toast({
        title: "Link copied!",
        description: "Conversation link has been copied to your clipboard.",
      });
    } catch (error) {
      console.error("Failed to copy link:", error);
      toast({
        title: "Failed to copy link",
        description: "Please try again or copy the URL manually.",
        variant: "destructive",
      });
    } finally {
      setIsCopying(false);
    }
  };

  return (
    <div className="border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={onMobileMenuToggle}
          >
            <Menu className="h-4 w-4" />
          </Button>

          {/* Conversation title and info */}
          <div className="flex flex-col space-y-1">
            <div className="flex items-center space-x-2">
              <h1 className="text-lg font-semibold text-slate-900 dark:text-white">
                {currentConversation?.title || "New Conversation"}
              </h1>

              {/* PersonaPill if persona is selected */}
              {currentPersona && <PersonaPill persona={currentPersona} />}

              {/* Status indicators */}
              {streamingState.isStreaming && (
                <Badge variant="secondary" className="text-xs">
                  <Zap className="w-3 h-3 mr-1" />
                  Thinking...
                </Badge>
              )}

              {streamingState.status === "tool_calls" && (
                <Badge variant="outline" className="text-xs">
                  <Settings className="w-3 h-3 mr-1" />
                  Processing...
                </Badge>
              )}
            </div>

            {/* Author and visibility info */}
            {currentConversation && (
              <div className="flex items-center space-x-3 text-xs text-slate-500 dark:text-slate-400">
                <div className="flex items-center space-x-1">
                  <User className="w-3 h-3" />
                  <span>
                    Created by {getAuthorDisplayName(currentConversation)}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  {getVisibilityIcon(currentConversation.visibility)}
                  <span className="capitalize">
                    {currentConversation.visibility}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Stop streaming button */}
          {streamingState.isStreaming && (
            <Button
              variant="outline"
              size="sm"
              onClick={onStopStreaming}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <StopCircle className="w-4 h-4 mr-1" />
              Stop
            </Button>
          )}

          {/* Share/Visibility controls - only for conversation owner */}
          {currentConversation &&
            isOwner(currentConversation) &&
            onUpdateVisibility && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Share2 className="w-4 h-4 mr-1" />
                    Share
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {/* Copy Link option */}
                  <DropdownMenuItem
                    onClick={() => copyConversationLink(currentConversation)}
                    disabled={
                      !canShareConversation(currentConversation) || isCopying
                    }
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    {isCopying ? "Copying..." : "Copy Link"}
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  {/* Visibility toggle - only for owners */}
                  {isOwner(currentConversation) && (
                    <>
                      <DropdownMenuItem
                        onClick={() => {
                          const newVisibility =
                            currentConversation.visibility === "private"
                              ? "public"
                              : "private";
                          onUpdateVisibility(
                            currentConversation.id,
                            newVisibility
                          );
                        }}
                      >
                        {currentConversation.visibility === "private" ? (
                          <>
                            <Globe className="w-4 h-4 mr-2" />
                            Make Public
                          </>
                        ) : (
                          <>
                            <Lock className="w-4 h-4 mr-2" />
                            Make Private
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                    </>
                  )}

                  <DropdownMenuItem disabled>
                    <span className="text-xs text-slate-500">
                      {currentConversation.visibility === "private"
                        ? "Only you can see this conversation"
                        : "All organization members can view this conversation"}
                    </span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

          {/* Settings button */}
          {currentConversation && (
            <Button variant="ghost" size="sm" onClick={onSettingsClick}>
              <Settings className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
