"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Building,
  ChevronDown,
  Crown,
  Shield,
  User,
  Plus,
  RefreshCw,
} from "lucide-react";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useDataRefresh } from "@/hooks/useDataRefresh";
import { cn } from "@/lib/utils";

interface OrganizationSelectorProps {
  className?: string;
}

export function OrganizationSelector({ className }: OrganizationSelectorProps) {
  const {
    currentOrganization,
    organizations,
    setCurrentOrganization,
    refreshOrganizations,
  } = useOrganization();
  const { refreshAllData } = useDataRefresh();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle refresh data
  const handleRefreshData = async () => {
    setIsRefreshing(true);
    try {
      await refreshAllData();
    } catch (error) {
      console.error("Failed to refresh data:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Create organization dialog state
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [createForm, setCreateForm] = useState({
    name: "",
    slug: "",
    description: "",
  });
  const [createError, setCreateError] = useState<string | null>(null);

  // Handle create organization
  const handleCreateOrganization = async () => {
    if (!createForm.name.trim() || !createForm.slug.trim()) {
      setCreateError("Name and slug are required");
      return;
    }

    setIsCreating(true);
    setCreateError(null);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/create-organization`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify(createForm),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create organization");
      }

      const data = await response.json();

      // Refresh organizations and set the new one as current
      await refreshOrganizations();

      // Close dialog and reset form
      setIsCreateDialogOpen(false);
      setCreateForm({ name: "", slug: "", description: "" });

      // Set the new organization as current
      if (data.data?.organization) {
        setCurrentOrganization(data.data.organization);
      }
    } catch (err: any) {
      setCreateError(err.message);
    } finally {
      setIsCreating(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role.toUpperCase()) {
      case "OWNER":
        return <Crown className="w-3 h-3 text-yellow-500" />;
      case "MANAGER":
        return <Shield className="w-3 h-3 text-blue-500" />;
      default:
        return <User className="w-3 h-3 text-slate-500" />;
    }
  };

  const getRoleBadge = (role: string) => {
    const roleUpper = role.toUpperCase();
    const colors = {
      OWNER:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      MANAGER: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      MEMBER:
        "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200",
    };

    return (
      <Badge
        variant="secondary"
        className={cn(
          "text-xs font-medium",
          colors[roleUpper as keyof typeof colors] || colors.MEMBER
        )}
      >
        {roleUpper}
      </Badge>
    );
  };

  if (!currentOrganization) {
    return (
      <div
        className={cn(
          "flex items-center justify-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg",
          className
        )}
      >
        <span className="text-sm text-slate-500 dark:text-slate-400">
          No organization selected
        </span>
      </div>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-between p-3 h-auto bg-slate-50 dark:bg-slate-800 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg",
              className
            )}
          >
            <div className="flex items-center space-x-3 min-w-0 flex-1">
              <Avatar className="w-8 h-8 flex-shrink-0">
                <AvatarImage src={currentOrganization.avatar} />
                <AvatarFallback className="bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400">
                  <Building className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0 text-left">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-slate-900 dark:text-white truncate">
                    {currentOrganization.name}
                  </span>
                  {getRoleIcon(currentOrganization.role)}
                </div>
                <div className="flex items-center space-x-2 mt-1">
                  {getRoleBadge(currentOrganization.role)}
                  {currentOrganization.plan && (
                    <Badge variant="outline" className="text-xs">
                      {currentOrganization.plan}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <ChevronDown className="w-4 h-4 text-slate-400 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="start"
          className="w-80 max-h-96 overflow-y-auto"
          sideOffset={8}
        >
          <DropdownMenuLabel className="px-3 py-2 text-sm font-semibold text-slate-900 dark:text-white">
            Switch Organization
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          {organizations.map((org) => (
            <DropdownMenuItem
              key={org.id}
              onClick={() => setCurrentOrganization(org)}
              className={cn(
                "p-3 cursor-pointer transition-colors duration-200 hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800",
                currentOrganization.id === org.id &&
                  "bg-indigo-50 dark:bg-indigo-900/20 border-l-2 border-indigo-500"
              )}
            >
              <div className="flex items-center space-x-3 w-full">
                <Avatar className="w-8 h-8 flex-shrink-0">
                  <AvatarImage src={org.avatar} />
                  <AvatarFallback className="bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400">
                    <Building className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-slate-900 dark:text-white truncate">
                      {org.name}
                    </span>
                    {getRoleIcon(org.role)}
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    {getRoleBadge(org.role)}
                    {org.plan && (
                      <Badge variant="outline" className="text-xs">
                        {org.plan}
                      </Badge>
                    )}
                  </div>
                </div>
                {currentOrganization.id === org.id && (
                  <div className="w-2 h-2 bg-indigo-600 rounded-full flex-shrink-0" />
                )}
              </div>
            </DropdownMenuItem>
          ))}

          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="p-3 cursor-pointer text-slate-600 dark:text-slate-400"
            onClick={handleRefreshData}
            disabled={isRefreshing}
          >
            <RefreshCw
              className={cn("w-4 h-4 mr-3", isRefreshing && "animate-spin")}
            />
            {isRefreshing ? "Refreshing..." : "Refresh Data"}
          </DropdownMenuItem>
          <DropdownMenuItem
            className="p-3 cursor-pointer text-indigo-600 dark:text-indigo-400"
            onClick={() => setIsCreateDialogOpen(true)}
          >
            <Plus className="w-4 h-4 mr-3" />
            Create New Organization
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Create Organization Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Organization</DialogTitle>
            <DialogDescription>
              Create a new organization to collaborate with your team.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={createForm.name}
                onChange={(e) => {
                  setCreateForm({ ...createForm, name: e.target.value });
                  // Auto-generate slug from name
                  const slug = e.target.value
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, "-")
                    .replace(/(^-|-$)/g, "");
                  setCreateForm((prev) => ({ ...prev, slug }));
                }}
                className="col-span-3"
                placeholder="My Organization"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="slug" className="text-right">
                Slug
              </Label>
              <Input
                id="slug"
                value={createForm.slug}
                onChange={(e) =>
                  setCreateForm({ ...createForm, slug: e.target.value })
                }
                className="col-span-3"
                placeholder="my-organization"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Input
                id="description"
                value={createForm.description}
                onChange={(e) =>
                  setCreateForm({ ...createForm, description: e.target.value })
                }
                className="col-span-3"
                placeholder="Optional description"
              />
            </div>
            {createError && (
              <div className="text-sm text-red-600 dark:text-red-400">
                {createError}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleCreateOrganization}
              disabled={isCreating}
            >
              {isCreating ? "Creating..." : "Create Organization"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
