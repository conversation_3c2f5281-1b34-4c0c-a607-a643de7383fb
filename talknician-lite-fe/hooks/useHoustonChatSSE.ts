import { useCallback, useRef, useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import {
  ChatMessage,
  ChatSettings,
  Conversation,
  ConversationFilter,
  ConversationVisibility,
} from "@/types/chat";
import {
  useChatStore,
  useSetCurrentConversation,
  useSetMessages,
  useAddMessage,
  useSetConversations,
  useAddConversation,
  useRemoveConversation,
  useSetChatSettings,
  useResetMessages,
} from "@/stores/useChatStore";
import { useStreamingStore } from "@/stores/useStreamingStore";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useConversationApi } from "@/api/conversations";
import { useChatApi } from "@/api/chat";
import {
  setLastConversationId,
  getConversationSettings as getStoredConversationSettings,
  setConversationSettings as setStoredConversationSettings,
  removeConversationSettings,
} from "@/utils/conversationStorage";
import { getConversationSettings } from "@/utils/personaUtils";
import { UploadedFileState } from "@/components/houston/ChatInput";

// Custom SSE client for POST requests
class CustomSSEClient {
  private controller: AbortController | null = null;
  private url: string;
  private options: any;

  constructor(url: string, options: any) {
    this.url = url;
    this.options = options;
    this.connect();
  }

  private async connect() {
    this.controller = new AbortController();

    try {
      const response = await fetch(this.url, {
        ...this.options,
        signal: this.controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (this.options.onopen) {
        this.options.onopen();
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error("No response body reader available");
      }

      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");

        // Keep the last incomplete line in buffer
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("event: ")) {
            // Store event type for next data line
            continue;
          } else if (line.startsWith("data: ")) {
            const data = line.slice(6).trim();
            if (data && this.options.onmessage) {
              try {
                this.options.onmessage({ data });
              } catch (error) {
                console.error("Error processing SSE message:", error);
              }
            }
          }
        }
      }
    } catch (error) {
      if (this.options.onerror && !this.controller?.signal.aborted) {
        this.options.onerror(error);
      }
    }
  }
}

export const useHoustonChatSSE = (
  filter?: ConversationFilter,
  authorId?: string,
  searchQuery?: string
) => {
  const { accessToken, user } = useAuth();
  const { currentOrganization } = useOrganization();
  const queryClient = useQueryClient();

  // Separate stores
  const { currentConversation, messages, conversations, chatSettings } =
    useChatStore();

  // Individual action hooks to avoid hydration issues
  const setCurrentConversation = useSetCurrentConversation();
  const setMessages = useSetMessages();
  const addMessage = useAddMessage();
  const setConversations = useSetConversations();
  const addConversation = useAddConversation();
  const removeConversation = useRemoveConversation();
  const setChatSettings = useSetChatSettings();
  const resetMessages = useResetMessages();

  const { streamingState, setStreamingState } = useStreamingStore();
  const [isLoadingConversation, setIsLoadingConversation] = useState(false);

  const sseClientRef = useRef<CustomSSEClient | null>(null);
  const conversationApi = useConversationApi();
  const chatApi = useChatApi();

  // React Query for conversations
  const conversationsQuery = useQuery({
    queryKey: ["conversations", currentOrganization?.id, filter, authorId],
    queryFn: () =>
      conversationApi.getConversations(
        currentOrganization!.id,
        filter,
        authorId
      ),
    enabled: !!currentOrganization && !!accessToken,
  });

  // React Query for chat settings
  const chatSettingsQuery = useQuery({
    queryKey: ["chatSettings", currentOrganization?.id],
    queryFn: () => chatApi.getChatSettings(currentOrganization!.id),
    enabled: !!currentOrganization && !!accessToken,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // React Query for conversation authors
  const authorsQuery = useQuery({
    queryKey: ["conversationAuthors", currentOrganization?.id],
    queryFn: () =>
      conversationApi.getConversationAuthors(currentOrganization!.id),
    enabled: !!currentOrganization && !!accessToken,
  });

  // Mutations
  const renameConversationMutation = useMutation({
    mutationFn: ({
      conversationId,
      title,
    }: {
      conversationId: string;
      title: string;
    }) => conversationApi.updateConversationTitle(conversationId, title),
    onSuccess: (updatedConversation) => {
      // Update the conversation in the store
      setConversations(
        conversations.map((conv) =>
          conv.id === updatedConversation.id ? updatedConversation : conv
        )
      );
      if (currentConversation?.id === updatedConversation.id) {
        setCurrentConversation(updatedConversation);
      }
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
    },
  });

  const createConversationMutation = useMutation({
    mutationFn: (data: {
      message?: string;
      settings: ChatSettings;
      visibility?: ConversationVisibility;
    }) => {
      if (!currentOrganization?.id) {
        throw new Error("No organization selected");
      }
      return conversationApi.createConversation(
        "New Conversation",
        currentOrganization.id,
        data.visibility || "private"
      );
    },
    onSuccess: (newConversation, { message, settings }) => {
      addConversation(newConversation);
      setCurrentConversation(newConversation);
      resetMessages(); // Clear messages for the new conversation
      queryClient.invalidateQueries({
        queryKey: ["conversations", currentOrganization?.id],
      });

      if (message && typeof message === "string") {
        const newTitle =
          message.length > 50 ? message.substring(0, 50) + "..." : message;
        renameConversationMutation.mutate({
          conversationId: newConversation.id,
          title: newTitle,
        });

        sendMessage({
          conversationId: newConversation.id,
          message,
          settings,
        });
      }
    },
  });

  const deleteConversationMutation = useMutation({
    mutationFn: (conversationId: string) =>
      conversationApi.deleteConversation(conversationId),
    onSuccess: async (_, conversationId) => {
      // Optimistically remove from local state
      removeConversation(conversationId);

      // Clean up localStorage
      removeConversationSettings(conversationId);

      // If this was the current conversation, clear it and localStorage
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(null);
        resetMessages();
        setLastConversationId(null);
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["conversations", currentOrganization?.id],
      });
    },
  });

  // Update conversation visibility mutation
  const updateVisibilityMutation = useMutation({
    mutationFn: ({
      conversationId,
      visibility,
    }: {
      conversationId: string;
      visibility: ConversationVisibility;
    }) => {
      return conversationApi.updateConversationVisibility(
        conversationId,
        visibility
      );
    },
    onSuccess: (updatedConversation) => {
      // Update the conversation in the store
      const conversations =
        queryClient.getQueryData<Conversation[]>([
          "conversations",
          currentOrganization?.id,
          filter,
        ]) || [];

      const updatedConversations = conversations.map((conv) =>
        conv.id === updatedConversation.id ? updatedConversation : conv
      );

      queryClient.setQueryData(
        ["conversations", currentOrganization?.id, filter],
        updatedConversations
      );

      // Update current conversation if it's the one being updated
      if (currentConversation?.id === updatedConversation.id) {
        setCurrentConversation(updatedConversation);
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["conversations", currentOrganization?.id],
      });
    },
  });

  // Load conversations effect
  useEffect(() => {
    if (conversationsQuery.data) {
      setConversations(conversationsQuery.data);
      // Note: Removed auto-selection logic to prevent infinite re-renders
      // Conversation selection is now handled by URL routing
    }
  }, [conversationsQuery.data, setConversations]);

  // Load chat settings effect
  useEffect(() => {
    if (chatSettingsQuery.data) {
      setChatSettings(chatSettingsQuery.data);
    }
  }, [chatSettingsQuery.data]);

  // Load specific conversation
  const loadConversation = useCallback(
    async (conversationId: string) => {
      if (!accessToken) return null;
      if (!currentOrganization?.id) return null;

      try {
        setIsLoadingConversation(true);
        setStreamingState({ error: null });
        const { conversation, messages } =
          await conversationApi.getConversation(
            conversationId,
            currentOrganization?.id
          );
        setCurrentConversation(conversation);
        setMessages(messages);
        if (conversation.user?.email === user?.email) {
          // Save this as the last accessed conversation
          setLastConversationId(conversationId);
        }

        // Load all settings for this conversation from localStorage
        const savedSettings = getStoredConversationSettings(conversationId);

        return savedSettings;
      } catch (error: any) {
        console.error("Failed to load conversation:", error);

        // Check if it's a 404 or 403 error (conversation not found or access denied)
        if (
          error?.response?.status === 404 ||
          error?.response?.status === 403
        ) {
          console.log("Conversation not found or access denied (404/403)");
          setStreamingState({
            error: "Conversation not found or access denied",
          });
        } else {
          console.log("Other error loading conversation:", error);
          setStreamingState({ error: "Failed to load conversation" });
        }
        return null;
      } finally {
        setIsLoadingConversation(false);
      }
    },
    [
      accessToken,
      conversationApi,
      setCurrentConversation,
      setMessages,
      setStreamingState,
      setIsLoadingConversation,
    ]
  );

  // Handle persona selection for current conversation

  const sendMessage = useCallback(
    async ({
      conversationId,
      message,
      settings,
      files,
    }: {
      conversationId: string;
      message: string;
      settings: ChatSettings;
      files?: UploadedFileState[];
    }) => {
      if (!accessToken) return;

      try {
        // If this is the first message, update the conversation title
        if (messages.length === 0) {
          const newTitle =
            message.length > 30 ? message.substring(0, 30) + "..." : message;
          renameConversationMutation.mutate({
            conversationId: conversationId,
            title: newTitle,
          });
        }
        setStreamingState({
          isStreaming: true,
          currentContent: "",
          loadingMessage: "Houston is typing",
          error: null,
          status: "connecting",
        });

        // Add user message immediately
        const userMessage: ChatMessage = {
          id: `temp-${Date.now()}`,
          content: message,
          role: "user",
          createdAt: new Date().toISOString(),
          messageDocuments: files || [],
        };
        addMessage(userMessage);

        // Merge persona system prompt with settings
        const finalSettings = settings || chatSettings;

        // Create SSE client
        const sseClient = new CustomSSEClient(
          `${process.env.NEXT_PUBLIC_API_URL}/api/chat/conversations/${conversationId}/messages/stream`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify({
              message,
              settings: finalSettings,
              files,
              organizationId: currentOrganization?.id,
            }),
            onopen: () => {
              console.log("SSE Connected");
              setStreamingState({ status: "connected" });
            },
            onmessage: (event: { data: string }) => {
              try {
                const data = JSON.parse(event.data);

                switch (data.type) {
                  case "connected":
                    console.log("SSE connection confirmed");
                    break;

                  case "content":
                    setStreamingState({
                      currentContent:
                        streamingState.currentContent + (data.content || ""),
                    });
                    break;

                  case "tool_calls_started":
                    setStreamingState({
                      loadingMessage:
                        data.content || data.status || "Processing...",
                      status: "tool_calls",
                    });
                    break;

                  case "completed":
                    // Add assistant message
                    const assistantMessage: ChatMessage = {
                      id: data.data?.id || Date.now().toString(),
                      content:
                        data.data?.content || streamingState.currentContent,
                      role: "assistant",
                      createdAt:
                        data.data?.createdAt || new Date().toISOString(),
                      references: data.data?.references || [],
                      annotations: data.data?.annotations || [],
                    };

                    addMessage(assistantMessage);
                    setStreamingState({
                      loadingMessage: "",
                      isStreaming: false,
                      currentContent: "",
                      error: null,
                      status: "completed",
                    });
                    break;

                  case "error":
                    setStreamingState({
                      isStreaming: false,
                      error:
                        data.content || data.data?.error || "An error occurred",
                    });
                    break;

                  case "end":
                    break;

                  default:
                    console.log("Unknown SSE event type:", data.type);
                }
              } catch (error) {
                console.error("Error parsing SSE message:", error, event.data);
              }
            },
            onerror: (error: any) => {
              console.error("SSE Error:", error);
              setStreamingState({
                isStreaming: false,
                error: "Connection error",
              });
            },
          }
        );

        sseClientRef.current = sseClient;
      } catch (error) {
        console.error("Failed to send message:", error);
        setStreamingState({
          isStreaming: false,
          currentContent: "",
          error: "Failed to send message",
          status: "error",
          loadingMessage: "",
        });
      }
    },
    [
      accessToken,
      streamingState.currentContent,
      chatSettings,
      addMessage,
      setStreamingState,
      messages,
      renameConversationMutation,
      currentOrganization,
    ]
  );

  const stopStreaming = useCallback(() => {
    if (sseClientRef.current) {
      sseClientRef.current = null;
      setStreamingState({
        isStreaming: false,
      });
    }
  }, [setStreamingState]);

  // Filter conversations based on search
  const filteredConversations = conversations.filter((conv) => {
    if (!searchQuery) return true;
    return conv.title.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const resetState = useCallback(() => {
    setCurrentConversation(null);
    setMessages([]);
    setStreamingState({
      isStreaming: false,
      currentContent: "",
      error: null,
      status: "end",
      loadingMessage: "",
    });
  }, [setCurrentConversation, setMessages, setStreamingState]);

  return {
    // State
    currentConversation,
    messages,
    conversations: filteredConversations,
    chatSettings,
    streamingState,
    authors: authorsQuery.data || [],

    // Loading states
    isLoadingConversations: conversationsQuery.isLoading,
    isLoadingConversation,
    isLoadingSettings: chatSettingsQuery.isLoading,
    isLoadingAuthors: <AUTHORS>

    // Actions
    loadConversation,
    createNewConversation: (
      message: string | undefined,
      settings: ChatSettings,
      visibility?: ConversationVisibility
    ) => {
      return new Promise((resolve, reject) => {
        createConversationMutation.mutate(
          { message, settings, visibility },
          {
            onSuccess: (conversation) => resolve(conversation),
            onError: (error) => reject(error),
          }
        );
      });
    },
    deleteConversation: (id: string) => deleteConversationMutation.mutate(id),
    renameConversation: (id: string, title: string) =>
      renameConversationMutation.mutate({ conversationId: id, title }),
    updateConversationVisibility: (
      id: string,
      visibility: ConversationVisibility
    ) => updateVisibilityMutation.mutate({ conversationId: id, visibility }),
    sendMessage,
    stopStreaming,
    resetState,
  };
};
