"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter, notFound } from "next/navigation";
import { X } from "lucide-react";
import { useHoustonChatSSE } from "@/hooks/useHoustonChatSSE";
import { usePersonas } from "@/hooks/usePersonas";
import { useSettingsStore } from "@/stores/useSettingsStore";
import { useAuth } from "@/contexts/AuthContext";
import {
  getConversationSettings,
  setConversationSettings,
} from "@/utils/personaUtils";
import { setConversationSettings as setStoredConversationSettings } from "@/utils/conversationStorage";
import { ConversationsSidebar } from "@/components/houston/ConversationsSidebar";
import { ChatHeader } from "@/components/houston/ChatHeader";
import { MessagesList } from "@/components/houston/MessagesList";
import { ChatInput, UploadedFileState } from "@/components/houston/ChatInput";
import { SettingsDialog } from "@/components/houston/SettingsDialog";

export default function ConversationPage() {
  const params = useParams();
  const router = useRouter();
  const conversationId = params.conversationId as string;

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isMobileConversationsOpen, setIsMobileConversationsOpen] =
    useState(false);
  const [conversationSearchQuery, setConversationSearchQuery] = useState("");
  const [conversationFilter, setConversationFilter] = useState<
    "my-chats" | "public-chats" | "all" | "by-author"
  >("all");
  const [selectedAuthorId, setSelectedAuthorId] = useState<
    string | undefined
  >();
  const { personas, getPersona, refreshPersonas } = usePersonas();
  const [selectedPersonaId, setSelectedPersonaId] = useState<string | null>(
    null
  );

  const {
    currentRequestWebSearch,
    currentRequestFileSearch,
    setCurrentRequestWebSearch,
    setCurrentRequestFileSearch,
  } = useSettingsStore();

  const currentPersona = getPersona(selectedPersonaId);
  const { user } = useAuth();

  // Use the integrated hook that combines SSE, Zustand, and React Query
  const {
    // State
    currentConversation,
    messages,
    conversations,
    streamingState,
    authors,

    // Loading states
    isLoadingConversations,

    // Actions
    loadConversation,
    createNewConversation,
    deleteConversation,
    sendMessage,
    stopStreaming,
    renameConversation,
    updateConversationVisibility,
    resetState,
  } = useHoustonChatSSE(conversationFilter, selectedAuthorId);

  // Load conversation when conversationId changes
  useEffect(() => {
    if (conversationId && conversationId !== "new") {
      console.log("run load conversation");
      loadConversation(conversationId)
        .then((settings) => {
          if (settings === null) {
            // Conversation not found or access denied
            notFound();
          }
        })
        .catch((e) => {
          notFound();
        });
    } else if (conversationId === "new") {
      resetState(); // Clear messages for the new conversation
    }
  }, []);

  const handleSelectConversation = async (newConversationId: string) => {
    router.push(`/houston/${newConversationId}`);
  };

  const handleCreateNew = () => {
    router.push("/houston/new");
  };

  const handleAuthorSelect = (authorId: string | undefined) => {
    setSelectedAuthorId(authorId);
  };

  // Load persona settings when conversation changes
  useEffect(() => {
    if (currentConversation) {
      const settings = getConversationSettings(currentConversation.id);
      if (settings?.personaId) {
        setSelectedPersonaId(settings.personaId);
        setCurrentRequestFileSearch(settings.fileSearch ?? true);
        setCurrentRequestWebSearch(settings.webSearch ?? false);
      } else {
        setSelectedPersonaId(null);
        setCurrentRequestFileSearch(true);
        setCurrentRequestWebSearch(false);
      }
    }
  }, [currentConversation]);

  // Save persona settings when they change
  const savePersonaSettings = (personaId: string | null) => {
    if (currentConversation) {
      setConversationSettings(currentConversation.id, {
        personaId: personaId,
        fileSearch: currentRequestFileSearch,
        webSearch: currentRequestWebSearch,
      });
    }
  };

  useEffect(() => {
    if (currentConversation) {
      const settings = {
        personaId: selectedPersonaId,
        fileSearch: currentRequestFileSearch,
        webSearch: currentRequestWebSearch,
      };

      // Save to both persona utils and localStorage
      setConversationSettings(currentConversation.id, settings);
      setStoredConversationSettings(currentConversation.id, settings);
    }
  }, [
    currentConversation,
    selectedPersonaId,
    currentRequestFileSearch,
    currentRequestWebSearch,
  ]);

  const handleSendMessage = async (
    message: string,
    files?: UploadedFileState[],
    visibility?: "private" | "public"
  ) => {
    const settings = {
      personality: currentPersona?.systemPrompt,
      documentSearch: true,
      enableWebSearch: currentRequestWebSearch,
    };

    if (!currentConversation || conversationId === "new") {
      // Create new conversation first, then send message
      try {
        const newConversation = await createNewConversation(
          message,
          settings,
          visibility
        );
        // Navigate to the new conversation
        if (
          newConversation &&
          typeof newConversation === "object" &&
          "id" in newConversation
        ) {
          router.push(`/houston/${(newConversation as any).id}`);
        }
      } catch (error) {
        console.error("Failed to create conversation:", error);
      }
      return;
    }

    await sendMessage({
      conversationId: currentConversation.id,
      message,
      settings,
      files,
    });
  };

  const handleDeleteConversation = (id: string) => {
    deleteConversation(id);
    // If we're currently viewing the deleted conversation, redirect to main Houston page
    // which will handle finding the next conversation or showing new conversation
    if (id === conversationId) {
      router.push("/houston");
    }
  };

  return (
    <div className="flex h-screen bg-slate-50 dark:bg-slate-900">
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:w-80 md:flex-col">
        <ConversationsSidebar
          conversations={conversations}
          currentConversation={currentConversation}
          conversationSearchQuery={conversationSearchQuery}
          isLoading={isLoadingConversations}
          currentUserId={user?.sub}
          filter={conversationFilter}
          authors={authors}
          selectedAuthorId={selectedAuthorId}
          onSearchChange={setConversationSearchQuery}
          onFilterChange={setConversationFilter}
          onAuthorSelect={handleAuthorSelect}
          onConversationSelect={handleSelectConversation}
          onCreateNew={handleCreateNew}
          onDeleteConversation={handleDeleteConversation}
          onRenameConversation={renameConversation}
          onUpdateVisibility={updateConversationVisibility}
        />
      </div>

      {/* Mobile Sidebar */}
      {isMobileConversationsOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setIsMobileConversationsOpen(false)}
          />
          <div className="relative w-80 h-full">
            <ConversationsSidebar
              conversations={conversations}
              currentConversation={currentConversation}
              conversationSearchQuery={conversationSearchQuery}
              isLoading={isLoadingConversations}
              currentUserId={user?.sub}
              filter={conversationFilter}
              authors={authors}
              selectedAuthorId={selectedAuthorId}
              onSearchChange={setConversationSearchQuery}
              onFilterChange={setConversationFilter}
              onAuthorSelect={handleAuthorSelect}
              onConversationSelect={(id) => {
                handleSelectConversation(id);
                setIsMobileConversationsOpen(false);
              }}
              onCreateNew={() => {
                handleCreateNew();
                setIsMobileConversationsOpen(false);
              }}
              onDeleteConversation={handleDeleteConversation}
              onRenameConversation={renameConversation}
              onUpdateVisibility={updateConversationVisibility}
            />
            <button
              onClick={() => setIsMobileConversationsOpen(false)}
              className="absolute top-4 right-4 p-2 text-slate-400 hover:text-slate-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        <ChatHeader
          currentConversation={currentConversation}
          streamingState={streamingState}
          currentUserId={user?.sub}
          onMobileMenuToggle={() => setIsMobileConversationsOpen(true)}
          onSettingsClick={() => setIsSettingsOpen(true)}
          onStopStreaming={stopStreaming}
          onUpdateVisibility={updateConversationVisibility}
          currentPersona={currentPersona}
        />

        <MessagesList
          messages={messages}
          streamingState={streamingState}
          className="flex-1"
        />

        <ChatInput
          onSend={handleSendMessage}
          onStop={stopStreaming}
          isStreaming={streamingState.isStreaming}
          webSearchEnabled={currentRequestWebSearch}
          onWebSearchToggle={setCurrentRequestWebSearch}
          fileSearchEnabled={currentRequestFileSearch}
          onFileSearchToggle={setCurrentRequestFileSearch}
          currentUserId={user?.sub}
          conversationOwnerId={currentConversation?.userId}
          conversationOwnerName={
            currentConversation?.user?.name || currentConversation?.user?.email
          }
        />
      </div>

      {/* Settings Dialog */}
      <SettingsDialog
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
        selectedPersonaId={selectedPersonaId}
        onPersonaSelect={(personaId) => {
          setSelectedPersonaId(personaId);
          savePersonaSettings(personaId);
        }}
        personas={personas}
        refreshPersonas={refreshPersonas}
      />
    </div>
  );
}
