"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import {
  getAuth0Client,
  loginWithPopup,
  logout as auth0Logout,
  getUser,
  getToken,
  isAuthenticated,
  SocialLoginOptions,
} from "@/lib/auth0";

interface Organization {
  id: string;
  name: string;
  slug: string;
  role: string;
}

interface User {
  sub: string;
  email: string;
  name: string;
  picture?: string;
  email_verified: boolean;
  organizations?: Organization[];
}

interface AuthContextType {
  user: User | null;
  accessToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  loginWithCredentials: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  loginWithMicrosoft: () => Promise<void>;
  logout: () => void;
  error: string | null;
  refreshToken: () => Promise<void>;
  refreshUserData: () => Promise<void>;
  handlePostLoginRedirect: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tokenExpiry, setTokenExpiry] = useState<number | null>(null);

  // Clear auth state helper function
  const clearAuthState = () => {
    setUser(null);
    setAccessToken(null);
    setIsUserAuthenticated(false);
    setTokenExpiry(null);
    setError(null);
    localStorage.removeItem("auth_token");
    localStorage.removeItem("auth_user");
    localStorage.removeItem("current_organization_id");
    console.log("Auth state cleared");
  };

  // Validate token helper function
  const validateStoredToken = async (token: string): Promise<User | null> => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        return {
          sub: data.data.user.id,
          email: data.data.user.email,
          name: data.data.user.name,
          picture: data.data.user.avatar,
          email_verified: true,
          organizations: data.data.user.organizations || [],
        };
      } else if (response.status === 401 || response.status === 403) {
        // Token is invalid/expired
        console.warn("Token validation failed - token is invalid/expired");
        clearAuthState();
        throw new Error("Token expired or invalid");
      } else {
        throw new Error(`Token validation failed: ${response.status}`);
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes("Token expired")) {
        throw error; // Re-throw auth errors
      }
      // Network errors - try fallback
      console.error("Network error during token validation:", error);
      const storedUser = localStorage.getItem("auth_user");
      if (storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          console.log(
            "Using stored user data as fallback due to network error"
          );
          return userData;
        } catch (parseError) {
          console.error("Failed to parse stored user data:", parseError);
          clearAuthState();
          throw new Error("Invalid stored user data");
        }
      }
      throw error;
    }
  };

  // Process invitations for social login
  const processInvitations = async (token: string): Promise<void> => {
    try {
      console.log("Processing pending invitations...");
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/process-invitations`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.data.processedInvitations.length > 0) {
          console.log("Processed invitations:", data.data.processedInvitations);
          // Refresh user data to get updated organizations
          const userData = await validateStoredToken(token);
          if (userData) {
            setUser(userData);
            localStorage.setItem("auth_user", JSON.stringify(userData));
          }
        }
      } else {
        console.warn("Failed to process invitations:", response.status);
      }
    } catch (error) {
      console.error("Error processing invitations:", error);
      // Don't fail login if invitation processing fails
    }
  };

  // Handle redirect after successful login
  const handlePostLoginRedirect = () => {
    const redirectTo = sessionStorage.getItem("auth_redirect_after_login");
    if (redirectTo) {
      sessionStorage.removeItem("auth_redirect_after_login");
      window.location.href = redirectTo;
    } else {
      window.location.href = "/houston";
    }
  };

  // Initialize Auth0 and check authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // First, check Auth0 authentication status (for social login)
        const auth0 = await getAuth0Client();

        // Handle redirect callback if coming back from Auth0
        if (window.location.search.includes("code=")) {
          await auth0.handleRedirectCallback();
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        }

        const auth0Authenticated = await isAuthenticated();
        const storedToken = localStorage.getItem("auth_token");

        // If Auth0 is authenticated (social login), prioritize that flow
        if (auth0Authenticated) {
          const userData = await getUser();
          console.log("get useer", userData);
          const token = await getToken();

          if (userData && token) {
            // Check if we already have this token stored (to avoid re-sync)
            if (storedToken !== token) {
              console.log("New social login token detected, syncing...");

              // Try to sync with backend - send user data from Auth0 directly
              try {
                console.log("📧 Sending user data to backend:", {
                  auth0Id: userData.sub,
                  email: userData.email,
                  name: userData.name,
                  picture: userData.picture,
                });

                // Try the new endpoint that bypasses JWT token issues
                const response = await fetch(
                  `${process.env.NEXT_PUBLIC_API_URL}/api/auth/social-create-user`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                      access_token: token,
                    }),
                  }
                );

                if (response.ok) {
                  const data = await response.json();
                  const userDataFromBackend = {
                    sub: data.data.user.id,
                    email: data.data.user.email,
                    name: data.data.user.name,
                    picture: data.data.user.avatar,
                    email_verified: userData.email_verified || false,
                    organizations: data.data.user.organizations || [],
                  };

                  setUser(userDataFromBackend);
                  localStorage.setItem("auth_token", token);
                  localStorage.setItem(
                    "auth_user",
                    JSON.stringify(userDataFromBackend)
                  );

                  // Process any pending invitations
                  await processInvitations(token);

                  console.log("Social login sync successful");
                } else {
                  console.warn("Backend sync failed, using Auth0 data");
                  // Fallback to Auth0 data
                  const fallbackUserData = {
                    sub: userData.sub || "",
                    email: userData.email || "",
                    name: userData.name || "",
                    picture: userData.picture,
                    email_verified: userData.email_verified || false,
                    organizations: [],
                  };
                  setUser(fallbackUserData);
                  localStorage.setItem("auth_token", token);
                  localStorage.setItem(
                    "auth_user",
                    JSON.stringify(fallbackUserData)
                  );
                }
              } catch (syncError) {
                console.error("Failed to sync social login:", syncError);
                // Continue with Auth0 data
                const fallbackUserData = {
                  sub: userData.sub || "",
                  email: userData.email || "",
                  name: userData.name || "",
                  picture: userData.picture,
                  email_verified: userData.email_verified || false,
                  organizations: [],
                };
                setUser(fallbackUserData);
                localStorage.setItem("auth_token", token);
                localStorage.setItem(
                  "auth_user",
                  JSON.stringify(fallbackUserData)
                );
              }
            } else {
              console.log(
                "Existing social login session found, refreshing user data..."
              );
              // Always fetch fresh user data from database for social login
              // to ensure we have the latest organization information
              try {
                const response = await fetch(
                  `${process.env.NEXT_PUBLIC_API_URL}/api/auth/social-create-user`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                      access_token: token,
                    }),
                  }
                );

                if (response.ok) {
                  const data = await response.json();
                  const freshUserData = {
                    sub: data.data.user.id,
                    email: data.data.user.email,
                    name: data.data.user.name,
                    picture: data.data.user.avatar,
                    email_verified: userData.email_verified || false,
                    organizations: data.data.user.organizations || [],
                  };

                  setUser(freshUserData);
                  localStorage.setItem(
                    "auth_user",
                    JSON.stringify(freshUserData)
                  );
                  console.log("Fresh user data loaded from database");
                } else {
                  console.warn(
                    "Failed to fetch fresh user data, using stored data"
                  );
                  // Fallback to stored user data if available
                  const storedUser = localStorage.getItem("auth_user");
                  if (storedUser) {
                    setUser(JSON.parse(storedUser));
                  } else {
                    setUser(userData as User);
                  }
                }
              } catch (error) {
                console.error("Error fetching fresh user data:", error);
                // Fallback to stored user data if available
                const storedUser = localStorage.getItem("auth_user");
                if (storedUser) {
                  setUser(JSON.parse(storedUser));
                } else {
                  setUser(userData as User);
                }
              }
            }

            setAccessToken(token);
            setIsUserAuthenticated(true);
            setTokenExpiry(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
            return; // Exit early for social login
          }
        }

        // If no Auth0 session but we have a stored token, validate it
        if (storedToken && !auth0Authenticated) {
          console.log("Validating stored token from credentials login");

          try {
            const userData = await validateStoredToken(storedToken);
            setUser(userData);
            setAccessToken(storedToken);
            setIsUserAuthenticated(true);
            console.log("Credentials login token valid");
            return; // Exit early if normal login token is valid
          } catch (error) {
            // validateStoredToken already cleared auth state if token was invalid
            console.error("Token validation failed:", error);
            if (
              error instanceof Error &&
              error.message.includes("Token expired")
            ) {
              setError("Your session has expired. Please log in again.");
            }
          }
        }

        // If we reach here, no valid authentication found
        console.log("No valid authentication found");
        setIsUserAuthenticated(false);
      } catch (error) {
        console.error("Auth initialization error:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Authentication initialization failed"
        );
        clearAuthState();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const loginWithCredentials = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call your backend API for credentials login
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/login`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, password }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Login failed");
      }

      const data = await response.json();

      // Set user data from your backend response
      setUser({
        sub: data.data.user.id,
        email: data.data.user.email,
        name: data.data.user.name,
        picture: data.data.user.avatar,
        email_verified: true,
        organizations: data.data.user.organizations || [],
      });

      setAccessToken(data.data.tokens.accessToken);
      setIsUserAuthenticated(true);

      // Store token and user data in localStorage for persistence
      localStorage.setItem("auth_token", data.data.tokens.accessToken);
      localStorage.setItem(
        "auth_user",
        JSON.stringify({
          sub: data.data.user.id,
          email: data.data.user.email,
          name: data.data.user.name,
          picture: data.data.user.avatar,
          email_verified: true,
          organizations: data.data.user.organizations || [],
        })
      );

      // Process any pending invitations for this user
      await processInvitations(data.data.tokens.accessToken);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Login failed");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await loginWithPopup({
        connection: "google-oauth2",
      });

      // The initializeAuth function will handle the sync automatically
      // after the popup completes, so we just need to wait for it
      console.log("Google login popup completed, waiting for sync...");

      // Brief delay to allow Auth0 state to settle
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Check if authentication was successful
      if (await isAuthenticated()) {
        console.log("Google login successful");
        setIsUserAuthenticated(true);
      } else {
        throw new Error("Authentication failed");
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "Google login failed");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithMicrosoft = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await loginWithPopup({
        connection: "windowslive",
      });

      // The initializeAuth function will handle the sync automatically
      // after the popup completes, so we just need to wait for it
      console.log("Microsoft login popup completed, waiting for sync...");

      // Brief delay to allow Auth0 state to settle
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Check if authentication was successful
      if (await isAuthenticated()) {
        console.log("Microsoft login successful");
        setIsUserAuthenticated(true);
      } else {
        throw new Error("Authentication failed");
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Microsoft login failed"
      );
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    clearAuthState();
    auth0Logout();
  };

  const refreshUserData = async () => {
    try {
      const storedToken = localStorage.getItem("auth_token");
      if (!storedToken) {
        console.warn("No token available for user data refresh");
        return;
      }

      // Check if this is a social login user
      if (await isAuthenticated()) {
        console.log("Refreshing user data for social login user...");
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/auth/social-create-user`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              access_token: storedToken,
            }),
          }
        );

        if (response.ok) {
          const data = await response.json();
          const userData = await getUser();
          const freshUserData = {
            sub: data.data.user.id,
            email: data.data.user.email,
            name: data.data.user.name,
            picture: data.data.user.avatar,
            email_verified: userData?.email_verified || false,
            organizations: data.data.user.organizations || [],
          };

          setUser(freshUserData);
          localStorage.setItem("auth_user", JSON.stringify(freshUserData));
          console.log("User data refreshed successfully");
        } else {
          console.warn("Failed to refresh user data from database");
        }
      } else {
        // For credentials login, use the existing validation endpoint
        console.log("Refreshing user data for credentials login user...");
        const userData = await validateStoredToken(storedToken);
        if (userData) {
          setUser(userData);
          localStorage.setItem("auth_user", JSON.stringify(userData));
          console.log("User data refreshed successfully");
        }
      }
    } catch (error) {
      console.error("Failed to refresh user data:", error);
    }
  };

  const refreshToken = async () => {
    try {
      const storedToken = localStorage.getItem("auth_token");
      if (!storedToken) {
        throw new Error("No token to refresh");
      }

      // Try to refresh using Auth0 client for social logins
      if (await isAuthenticated()) {
        const auth0 = await getAuth0Client();
        const newToken = await auth0.getTokenSilently();
        setAccessToken(newToken);
        localStorage.setItem("auth_token", newToken);

        // Update token expiry (24 hours from now)
        setTokenExpiry(Date.now() + 24 * 60 * 60 * 1000);

        // Also refresh user data to get latest information
        await refreshUserData();
        return;
      }

      // For credentials login, we need a refresh endpoint
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/refresh`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${storedToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setAccessToken(data.data.accessToken);
        localStorage.setItem("auth_token", data.data.accessToken);
        setTokenExpiry(Date.now() + data.data.expiresIn * 1000);
      } else if (response.status === 401 || response.status === 403) {
        // Refresh failed due to invalid token, clear auth and show error
        console.warn("Token refresh failed - token invalid/expired");
        setError("Your session has expired. Please log in again.");
        clearAuthState();
      } else {
        // Other refresh errors, just logout
        console.error("Token refresh failed with status:", response.status);
        clearAuthState();
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      setError("Your session has expired. Please log in again.");
      clearAuthState();
    }
  };

  // Auto-refresh token before expiry
  useEffect(() => {
    if (!tokenExpiry || !accessToken) return;

    const timeUntilExpiry = tokenExpiry - Date.now();
    const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 60000); // 5 min before expiry, min 1 min

    const timer = setTimeout(() => {
      refreshToken();
    }, refreshTime);

    return () => clearTimeout(timer);
  }, [tokenExpiry, accessToken]);

  const value: AuthContextType = {
    user,
    accessToken,
    isLoading,
    isAuthenticated: isUserAuthenticated,
    loginWithCredentials,
    loginWithGoogle,
    loginWithMicrosoft,
    logout: handleLogout,
    error,
    refreshToken,
    refreshUserData,
    handlePostLoginRedirect,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
